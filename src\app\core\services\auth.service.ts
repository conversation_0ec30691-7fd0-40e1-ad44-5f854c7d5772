import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { LocalStorageService, TokenData } from './local-storage.service';
import { environment } from '../../../app/environments/environment';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  refresh: string;
  access: string;
  user_id: number;
  email: string;
  is_staff: boolean;
}

export interface RefreshTokenResponse {
  access: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<any>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(
    private http: HttpClient,
    private localStorageService: LocalStorageService,
    private router: Router
  ) {
    // Check if user is already logged in on service initialization
    this.checkAuthenticationStatus();
  }

  private checkAuthenticationStatus(): void {
    const userData = this.localStorageService.getUserData();
    const hasValidToken = this.localStorageService.hasValidToken();
    
    if (userData && hasValidToken) {
      this.currentUserSubject.next(userData);
      this.isAuthenticatedSubject.next(true);
    }
  }

  login(credentials: LoginRequest): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${environment.apiUrl}/api/token/`, credentials)
      .pipe(
        tap(response => {
          // Store tokens and user data
          this.localStorageService.setTokens(response);
          
          // Update authentication state
          const userData = {
            user_id: response.user_id,
            email: response.email,
            is_staff: response.is_staff
          };
          
          this.currentUserSubject.next(userData);
          this.isAuthenticatedSubject.next(true);
        }),
        catchError(this.handleError)
      );
  }

  refreshToken(): Observable<RefreshTokenResponse> {
    const refreshToken = this.localStorageService.getRefreshToken();
    
    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    return this.http.post<RefreshTokenResponse>(`${environment.apiUrl}/api/token/refresh/`, {
      refresh: refreshToken
    }).pipe(
      tap(response => {
        this.localStorageService.updateAccessToken(response.access);
      }),
      catchError(error => {
        // If refresh fails, logout user
        this.logout();
        return throwError(() => error);
      })
    );
  }

  logout(): void {
    this.localStorageService.clearTokens();
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(['/login']);
  }

  isAuthenticated(): boolean {
    return this.localStorageService.hasValidToken();
  }

  getCurrentUser(): any {
    return this.currentUserSubject.value;
  }

  getAccessToken(): string | null {
    return this.localStorageService.getAccessToken();
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      if (error.status === 401) {
        errorMessage = 'Invalid email or password';
      } else if (error.status === 400) {
        errorMessage = 'Please check your input and try again';
      } else if (error.status === 500) {
        errorMessage = 'Server error. Please try again later';
      } else {
        errorMessage = `Error: ${error.message}`;
      }
    }
    
    return throwError(() => new Error(errorMessage));
  }
}
