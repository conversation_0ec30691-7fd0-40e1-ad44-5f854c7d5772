import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
  selector: 'app-registration',
  standalone: false,
  templateUrl: './registration.html',
  styleUrl: './registration.css'
})
export class Registration {
  registrationForm: FormGroup;
  verificationForm: FormGroup;
  showCodeField = false;
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  registeredEmail = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.registrationForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    this.verificationForm = this.fb.group({
      code: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  onRegisterClick() {
    if (this.registrationForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';

      const credentials = this.registrationForm.value;

      this.authService.register(credentials).subscribe({
        next: (response) => {
          this.registeredEmail = response.email;
          this.showCodeField = true;
          this.isLoading = false;
          this.successMessage = `Код подтверждения отправлен на ${response.email}`;
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Ошибка при регистрации';
        }
      });
    } else {
      this.markFormGroupTouched(this.registrationForm);
    }
  }

  onVerifyCode() {
    if (this.verificationForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';

      const verificationData = {
        email: this.registeredEmail,
        code: this.verificationForm.value.code
      };

      this.authService.verifyCode(verificationData).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.successMessage = response.detail;
          // Redirect to login after successful verification
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 2000);
        },
        error: (error) => {
          this.isLoading = false;
          if (error.error && error.error.non_field_errors) {
            this.errorMessage = error.error.non_field_errors[0];
          } else {
            this.errorMessage = error.message || 'Ошибка при проверке кода';
          }
        }
      });
    } else {
      this.markFormGroupTouched(this.verificationForm);
    }
  }

  resendCode() {
    // Resend the registration request to get a new code
    const credentials = {
      email: this.registeredEmail,
      password: this.registrationForm.value.password
    };

    this.isLoading = true;
    this.errorMessage = '';

    this.authService.register(credentials).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.successMessage = `Новый код отправлен на ${response.email}`;
        this.verificationForm.reset();
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || 'Ошибка при повторной отправке кода';
      }
    });
  }

  getFieldError(fieldName: string, formGroup: FormGroup = this.registrationForm): string {
    const field = formGroup.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return 'Это поле обязательно';
      }
      if (field.errors['email']) {
        return 'Введите корректный email';
      }
      if (field.errors['minlength']) {
        return `Минимум ${field.errors['minlength'].requiredLength} символов`;
      }
      if (field.errors['pattern']) {
        return 'Код должен содержать 6 цифр';
      }
    }
    return '';
  }

  isFieldInvalid(fieldName: string, formGroup: FormGroup = this.registrationForm): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.errors && field.touched);
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }
}
